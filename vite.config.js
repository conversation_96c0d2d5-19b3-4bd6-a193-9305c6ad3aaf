import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Disable fast refresh to prevent React hook issues
      fastRefresh: false,
      // Use React 18 JSX transform
      jsxRuntime: 'automatic',
      // Ensure proper React import
      jsxImportSource: 'react'
    })
  ],
  build: {
    // Optimize bundle size
    rollupOptions: {
      output: {
        manualChunks: {
          // Separate vendor chunks for better caching
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'animation-vendor': ['framer-motion'],
          'ui-vendor': ['lucide-react'],
        }
      }
    },
    // Enable minification
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
      }
    },
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
    // Enable source maps for debugging (disable in production)
    sourcemap: false,
  },
  // Optimize dev server
  server: {
    port: 5173,
    host: true,
    hmr: {
      port: 5173,
      overlay: false // Disable overlay to prevent React conflicts
    },
    // Force full reload instead of HMR for React files
    watch: {
      usePolling: true
    }
  },
  // Enable compression
  esbuild: {
    drop: ['console', 'debugger'], // Remove console.logs
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'lucide-react'
    ],
    exclude: ['sharp'], // Exclude server-side dependencies
    force: true // Force re-optimization
  },
  // Resolve configuration
  resolve: {
    dedupe: ['react', 'react-dom'], // Prevent duplicate React instances
    alias: {
      // Force single React instance
      'react': 'react',
      'react-dom': 'react-dom'
    }
  }
})
