import React, { createContext, useContext } from 'react';

// Create notification context
const NotificationContext = createContext();

// Types of notifications
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// Safe Notification Provider that doesn't use hooks initially
export const SafeNotificationProvider = ({ children }) => {
  // Simple state management without hooks for now
  const notifications = [];
  
  const addNotification = (message, type = NOTIFICATION_TYPES.INFO, duration = 5000) => {
    console.log('Notification:', message, type);
    // For now, just log notifications
    // We'll implement proper state management once React is stable
  };

  const removeNotification = (id) => {
    console.log('Remove notification:', id);
  };

  const clearNotifications = () => {
    console.log('Clear all notifications');
  };

  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications
  };

  return React.createElement(
    NotificationContext.Provider,
    { value },
    children
  );
};

// Hook to use notification context
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    // Return safe fallback
    return {
      notifications: [],
      addNotification: (message) => console.log('Notification:', message),
      removeNotification: () => {},
      clearNotifications: () => {}
    };
  }
  return context;
};

export default SafeNotificationProvider;
