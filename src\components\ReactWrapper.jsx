import React, { Suspense } from 'react';

// React Wrapper to ensure proper React context
const ReactWrapper = ({ children }) => {
  // Ensure React is properly initialized
  if (typeof React === 'undefined' || !React.useState) {
    console.error('React not properly initialized');
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <div className="text-4xl mb-4">⚠️</div>
          <h2 className="text-xl mb-2">Loading...</h2>
          <p className="text-gray-400">Initializing React components</p>
        </div>
      </div>
    );
  }

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading application...</p>
        </div>
      </div>
    }>
      {children}
    </Suspense>
  );
};

export default ReactWrapper;
